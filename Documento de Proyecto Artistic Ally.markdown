# Documento de Proyecto: Artistic Ally

**Versión**: 0.1  
**Fecha**: 31 de mayo de 2025  
**Autor**: Grok 3, en colaboración con el usuario

## 1. Introducción y Visión

**Artistic Ally** es una aplicación innovadora diseñada para asistir a pintores de todos los niveles —aficionados, profesionales y educadores— en la creación, aprendizaje y colaboración artística. La aplicación combina visión por computadora, proyección guiada, comandos de voz y herramientas de análisis para integrarse de forma natural en el flujo creativo del pintor, respetando su ritmo y potenciando sus habilidades. La visión a largo plazo es evolucionar hacia una plataforma web colaborativa que no solo asista en la pintura, sino que fomente una comunidad global de artistas, con funcionalidades educativas y de intercambio.

**Objetivos principales**:
- Simplificar la planificación y ejecución de obras pictóricas mediante análisis de imagen y proyección guiada.
- Facilitar el aprendizaje de técnicas artísticas a través de guías personalizadas y ejercicios dirigidos.
- Ofrecer herramientas para la autoevaluación y el seguimiento del progreso.
- Crear un entorno colaborativo para maestros, alumnos y comunidades artísticas.
- Ser accesible, intuitivo y adaptable a diferentes estilos y niveles de experiencia.

## 2. Público Objetivo

- **Pintores aficionados**: Buscan apoyo para mejorar composición, color y técnica.
- **Estudiantes de arte**: Necesitan herramientas para aprender y practicar de forma guiada.
- **Artistas profesionales**: Quieren optimizar su flujo de trabajo y explorar nuevas ideas.
- **Profesores de arte e instituciones educativas**: Buscan plataformas para enseñar y supervisar alumnos de manera interactiva.

## 3. Funcionalidades Clave

El desarrollo se plantea en tres fases progresivas, con un enfoque modular para garantizar escalabilidad y facilidad de implementación.

### **Fase 1: MVP (Producto Mínimo Viable) - Asistente Básico Local**

**Objetivo**: Crear una aplicación funcional que permita a los usuarios cargar una imagen, calibrar su setup y recibir guías básicas proyectadas.

- **Gestión de Imagen de Referencia**:
  - Carga de imágenes vía drag & drop o selección de archivo.
  - Herramientas de encuadre: zoom, pan y recorte manual con vista previa interactiva.
  - Análisis compositivo básico: superposición de guías (regla de los tercios, sección áurea, diagonales dinámicas).
  - Sugerencias automáticas de recorte basadas en puntos de interés (detección de rostros/objetos con OpenCV).

- **Tratamiento de Fondos**:
  - Desenfoque gaussiano para simplificar fondos.
  - (Opcional) Eliminación básica de elementos distractores usando segmentación simple.

- **Sistema de Calibración Cámara-Proyector**:
  - Proyección de una grilla numerada o patrón QR para calibración inicial.
  - Detección automática o manual de las esquinas del lienzo.
  - Guardado de la matriz de transformación para mapear píxeles de cámara a proyector.
  - Recalibración rápida mediante marcadores físicos (pequeñas pegatinas en las esquinas del lienzo).

- **Proyección de Guías Básicas**:
  - Proyección de contornos/croquis de la imagen de referencia sobre el lienzo.
  - Opción de activar/desactivar guías específicas (ej. "solo contornos").

- **Captura de Progreso (Snapshots)**:
  - Captura manual del estado del lienzo vía webcam con un comando de voz o botón.
  - Almacenamiento local de snapshots con timestamps.

- **Interfaz de Usuario (UI) Dual**:
  - **Monitor principal**: Vista de la imagen de referencia, controles de la app y vista en tiempo real de la webcam.
  - **Proyector**: Salida de guías específicas (contornos, zonas de corrección).
  - Interfaz intuitiva con drag & drop, zoom y controles gestuales.

- **Comandos de Voz Básicos**:
  - Comandos simples como "mostrar guías", "ocultar guías", "capturar progreso".
  - Implementación local usando Python SpeechRecognition con pocketsphinx.

- **Generación de Vídeo Stop-Motion**:
  - Creación automática de un vídeo stop-motion a partir de los snapshots almacenados.
  - Exportación en formatos estándar (MP4) con opciones básicas de velocidad y resolución.

### **Fase 2: Funcionalidades Esenciales y Web App Inicial**

**Objetivo**: Ampliar las capacidades de análisis, personalización y accesibilidad, introduciendo una versión web básica.

- **Análisis de Color Avanzado**:
  - Extracción de la paleta de colores dominante usando algoritmos de clustering (K-means).
  - Visualización interactiva de la paleta en el monitor.
  - Sugerencia de armonías de color (complementarios, análogos, triadas) para explorar variaciones creativas.
  - (Futuro) Sugerencia de mezclas físicas basada en los colores base del usuario (ej. tubos de óleo estándar).

- **Filtros Estilísticos**:
  - Aplicación de filtros tipo Photoshop (lápiz, carboncillo, acuarela, óleo) para visualizar la imagen de referencia en diferentes estilos.
  - Ayuda al pintor a entender texturas y acabados según la técnica elegida.

- **Perfiles de Textura del Lienzo**:
  - Calibración inicial para detectar la textura del lienzo (algodón, lino, papel acuarela).
  - Normalización de capturas para reducir el impacto de la textura en las comparaciones.
  - Opción de "pintar una muestra" con colores base para calibrar la interacción de pigmentos con la luz del proyector.

- **Secuenciación por Técnicas**:
  - Perfiles predefinidos para técnicas como acuarela (aguadas claras → medios tonos → oscuros) y óleo (sombras → medios tonos → luces).
  - Proyección de máscaras progresivas para guiar al pintor en cada etapa.
  - Personalización de etapas por el usuario (ej. "dividir en 5 pasos").

- **"Capas" de Proyección Inteligentes**:
  - Activación/desactivación selectiva de guías proyectadas (contornos, sombras, luces, zonas de color específicas).
  - Ejemplo: "mostrar solo sombras profundas" o "resaltar medios tonos cálidos".

- **Historial de Análisis y "Deshacer" Visual**:
  - Guardado de diferentes configuraciones de análisis (recortes, filtros, guías).
  - Posibilidad de volver a una configuración anterior sin reprocesar.

- **Comandos de Voz Contextuales**:
  - Comandos adaptados al modo activo (ej. "más saturación" en modo análisis de color).
  - Ejemplo: "enfocar en el cielo", "mostrar solo contornos".

- **Transición a Web App**:
  - Backend en Python con FastAPI, frontend en React/Vue con TailwindCSS.
  - Acceso a webcam vía WebRTC, proyector como segunda pantalla vía Screen Capture API.
  - Procesamiento híbrido: filtros básicos en navegador (OpenCV.js), análisis pesado en servidor.
  - Sistema básico de cuentas de usuario para guardar proyectos localmente (SQLite).

### **Fase 3: Funcionalidades Avanzadas, IA y Colaboración (Premium)**

**Objetivo**: Convertir Artistic Ally en una plataforma educativa y comunitaria con integración de IA y funcionalidades colaborativas.

- **Integración de Modelos ML/AI**:
  - Análisis avanzado de color y estilo usando Stable Diffusion (ControlNet) para variaciones estilísticas.
  - Clasificación de técnicas artísticas con MobileNet o EfficientNet.
  - Mejora de calidad de imagen con Real-ESRGAN.
  - Explorador de "Estilos Maestros" (reinterpretación de la imagen al estilo de Van Gogh, Monet, etc.).
  - Detección de patrones de error recurrentes para sugerencias personalizadas.

- **Módulo de Desafíos y Estudios Dirigidos**:
  - Ejercicios predefinidos como "estudio de valores tonales" o "práctica de colores complementarios".
  - Proyección guiada para cada ejercicio con objetivos claros.
  - Galería de resultados para compartir con la comunidad.

- **Comparativa de Progreso Visual**:
  - Alineación automática de snapshots con la imagen de referencia para comparar proporciones y valores.
  - Visualización side-by-side en el monitor.

- **Análisis de Composición Interactivo**:
  - Herramientas para ajustar el encuadre manualmente con feedback en tiempo real sobre equilibrio compositivo y puntos focales.
  - Sugerencias dinámicas basadas en líneas de dirección y distribución de peso visual.

- **Modo Foco**:
  - Proyección de luz suave en la zona donde se detecta la punta del pincel (requiere tracking ligero, opcional para premium).

- **Funcionalidades Colaborativas**:
  - **Modo Maestro-Alumno**:
    - Vista compartida del lienzo del alumno vía WebRTC.
    - Capa de anotaciones en tiempo real (dibujo, texto, notas de voz).
    - Grabación de sesiones y "rebobinado" para revisar el proceso.
  - **Clases Virtuales**:
    - Supervisión de múltiples alumnos en una cuadrícula.
    - Modo demo para que el profesor muestre técnicas en tiempo real.
  - **Comunidad**:
    - Galería de progresos y peer review.
    - Retos semanales con imágenes de referencia compartidas.
    - Compartir perfiles de hardware y calibraciones optimizadas.

- **Plataforma Web Completa**:
  - Workspace persistente con sincronización en la nube (PostgreSQL).
  - Perfiles de usuario personalizados (técnicas, hardware, preferencias).
  - Modo offline limitado para usuarios premium (descarga de modelos ligeros).
  - Autenticación con OAuth/JWT para seguridad.

## 4. Stack Tecnológico

- **Backend**: Python con FastAPI (APIs REST y WebSocket).
- **Procesamiento de Imagen**: OpenCV, Pillow, Scikit-image, NumPy.
- **Frontend**: React con TailwindCSS (empaquetado en Electron para fase inicial, puro web después).
- **Control de Hardware**:
  - Cámara: WebRTC (web), OpenCV (local).
  - Proyector: Screen Capture API (web), APIs específicas del proyector (local).
- **Reconocimiento de Voz**: Web Speech API (web), SpeechRecognition con pocketsphinx (local).
- **Base de Datos**: SQLite (MVP), PostgreSQL (web, para usuarios y proyectos).
- **Contenerización**: Docker, Docker Compose para desarrollo y despliegue.
- **ML/AI (Fase 3)**: PyTorch/TensorFlow, Hugging Face Transformers, Ollama para modelos locales.
- **Web Tecnologías**: WebAssembly para OpenCV.js, Three.js para calibración 3D, Canvas API/WebGL para gráficos.

## 5. Estrategia de Monetización

- **Tier Gratuito**:
  - Funcionalidades básicas del MVP y Fase 2 (análisis simple, un proyecto activo, sin guardado permanente).
  - Vídeo stop-motion en baja resolución.
- **Tier Premium Individual**:
  - Persistencia de proyectos, historial completo, análisis avanzado (ML), perfiles de textura, secuencias personalizadas.
  - Vídeo stop-motion en alta calidad.
- **Tier Premium Educativo**:
  - Funcionalidades colaborativas (maestro-alumno, clases virtuales).
  - Gestión de múltiples alumnos y grabación de sesiones.
- **Tier Institucional**:
  - Licencias para escuelas de arte con dashboards para profesores.
  - Estadísticas de progreso de alumnos.
- **Monetización Adicional**:
  - Marketplace de filtros/técnicas creados por usuarios.
  - Clases magistrales grabadas por artistas reconocidos.
  - Sesiones 1-on-1 con profesores (créditos).

## 6. Metodología de Desarrollo

- **Progresiva y Modular**: Implementar en fases, priorizando funcionalidades de alto impacto.
- **Iterativa**: Basada en feedback continuo de usuarios reales (artistas, profesores).
- **Desarrollo Ágil**: Sprints cortos para iterar sobre el MVP.
- **Pruebas de Usabilidad**: Validar la calibración, proyección y comandos de voz con un grupo pequeño de pintores.
- **Código Limpio**: Estructura modular (image_processor, projection_manager, voice_controller, ui_manager, profile_manager).

## 7. Próximos Pasos

1. **Definir el Alcance Final del MVP**:
   - Priorizar carga de imagen, calibración, proyección de contornos y snapshots.
   - Especificar comandos de voz iniciales.
2. **Diseñar el Flujo de Usuario**:
   - Crear wireframes para la UI (carga, calibración, proyección, snapshots).
   - Mapear comandos de voz y su contexto.
3. **Estructura de Datos Inicial**:
   - Definir formato para proyectos (imagen, calibración, snapshots).
   - Esquema básico para SQLite.
4. **Prototipo Técnico**:
   - Implementar carga de imagen y análisis compositivo.
   - Probar calibración cámara-proyector en un entorno controlado.
   - Generar un vídeo stop-motion básico desde snapshots.
5. **Validación con Usuarios**:
   - Reclutar un grupo pequeño de pintores para pruebas.
   - Ajustar la UX según feedback.

## 8. Consideraciones Adicionales

- **Multilingüismo**: Soporte para comandos de voz y UI en múltiples idiomas, con énfasis en términos artísticos (ej. "aguada", "claroscuro").
- **Condiciones Ambientales**: Garantizar robustez ante variaciones de luz mediante calibración lumínica inicial.
- **Escalabilidad Web**: Planificar desde el inicio para minimizar latencia en funciones avanzadas (WebSocket, CDN para assets).
- **Accesibilidad**: Interfaz intuitiva para usuarios no técnicos, con tutoriales guiados.